新建活动类型6204命名FigureCollection
1. 系统概述
本活动为抽奖（Gacha）玩法，玩家通过消耗多种指定素材来“制作手办”。
制作分为“普通制作”和“细心制作”两种类型，对应不同的奖池和消耗。
首次获得某个手办时，会激活该手办对应的、在活动期间生效的全局增益效果（Buff）。
重复获得已有的手办时，会获得额外的随机奖励抽奖。
系统包含完整的抽奖限制（至少抽奖次数>=手办上限才能获得）与保底机制（>=特定次数内必定获得）。每个抽卡次数独立存储抽中重置
2. 玩法规则
1. 制作手办（抽奖请求）
玩家发起制作请求，请求中包含制作类型make_type（1为普通，2为细心）和制作次数count（1次或10次）。
服务器根据make_type查询WuzhiFigureMake_手办制作表，获取对应的item_cost字段，验证并扣除玩家持有的素材。若素材不足，则操作失败。
服务器循环执行count次抽奖逻辑，每次都生成一个手办结果。
2. 抽奖与保底逻辑
奖池确定：根据请求的make_type，从WuzhiFigureMake_手办制作表中读取figure_output字段二维数组。该字段定义了当前奖池，配置格式为：手办id,权重,限制次数(至少抽奖次数),保底次数|...。
保底判断：
在进行权重随机前，优先检查保底。服务器需为每个玩家记录make_type下每个限制次数>0或者保底次数>0的手办的“累计未抽中次数”。
遍历当前奖池中的所有手办，若某个手办的“累计未抽中次数” ≥ 其配置的“保底次数”，则本次抽奖结果强制为该手办。
保底冲突处理：若同时有多个手办满足保底条件，则以“保底次数”需求值更高的手办为优先。若保底次数也相同，则按手办id从小到大的顺序选择。
权重随机：
若无保底触发，则在奖池内进行权重随机。
随机前，需要排除掉玩家获得次数未达到“限制次数”的手办。
“限制次数”或“保底次数”配置为0时，表示该机制不生效。
计数更新：
一次抽奖结束后，将本次抽出的手办的“累计未抽中次数”计数重置为0。
奖池内所有其他手办的“累计未抽中次数”计数+1。
3. 奖励生成与状态变更
对每次抽奖生成的手办id，判断玩家是否为首次获得。
首次获得：
根据手办id查询WuzhiFigure_无职手办表，读取其关联的privilege字段一维数组，增益类型|增益子类型|参数。
激活该增益类型对应的增益效果，增益的生效逻辑需应用到游戏全局。不同的手办相同的效果数值直接叠加
重复获得：
玩家获得一个手办道具，和一次“额外奖励”抽奖。
根据重复获得的手办id，查询WuzhiFigure_无职手办表，读取其reward字段。该字段定义了额外奖励的独立奖池，配置格式为：奖励id,数量,权重,限制,保底|...。
对此额外奖励奖池，执行一次独立的、带有限制和保底的抽奖，并将生成的奖励发给玩家。服务器需要为每个手办的额外奖励池维护独立的抽奖计数。限制次数>0或者保底次数>0才需要计数
4. 增益（Buff）规则
增益在首次获得手办时激活，活动结束时失效。可以参考AngelDevelopEffect这个类，也是用来奖励提示的
{101,0,缩短分钟数}//ActivityControlSlime活动中体力恢复间隔缩短
{102,0,提升百分比}//ActivityControlSlime活动中冒险获得的金币数量提升
{103,0,额外道具数量}//ActivityControlBoss领取任务奖励时额外获得攻击BOSS道具
{104,角色id,提升百分比}//ActivityControlWuzhiLove获得好感度提升
{201,副本类型,奖励提升百分比}//COINCHAPTER_2和DIAMONDCHAPTER_3和LEGACYTEAMCHAPTER_8奖励提升
{202,4,额外星渊石数量}//MOUNTCHAPTER_9 奖励增加x个星渊石
{205,玩家属性类型,数值}直接加到玩家属性
{207,0,上限提升数量}加竞技场入场券上限
//协议我已经设计添加完成如下:
// 无职联动-手办收藏活动信息
message act_wuzhi_figure_info_c2s {
    option (msgid) = 6710;
    uint32 act_type = 1;
}

message act_wuzhi_figure_info_s2c {
    option (msgid) = 6711;
    uint32 act_type = 1;
    repeated p_key_value owned_figures = 2;
}


// 无职联动-制作手办
message act_wuzhi_figure_make_c2s {
    option (msgid) = 6712;
    uint32 act_type = 1;
    uint32 make_type = 2; // 制作类型 (1:普通制作, 2:细心制作)
    uint32 count = 3;     // 制作次数 (1次或10次)
}

message act_wuzhi_figure_make_s2c {
    option (msgid) = 6713;
    uint32 act_type = 1;
    repeated p_reward_status_list results = 2;//status:手办sn
}

